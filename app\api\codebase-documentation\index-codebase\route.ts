import { NextRequest, NextResponse } from 'next/server';
import { codebaseIndexingTool } from '../../../../lib/tools/codebase-indexing-tool';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.userId) {
      return NextResponse.json(
        { success: false, error: 'userId is required' },
        { status: 400 }
      );
    }

    if (!body.rootPath) {
      return NextResponse.json(
        { success: false, error: 'rootPath is required' },
        { status: 400 }
      );
    }

    if (!body.projectName) {
      return NextResponse.json(
        { success: false, error: 'projectName is required' },
        { status: 400 }
      );
    }

    console.log('🚀 Starting direct codebase indexing for:', {
      userId: body.userId,
      rootPath: body.rootPath,
      projectName: body.projectName
    });

    // Index the codebase using direct vector embedding approach
    const result = await codebaseIndexingTool.indexCodebase({
      rootPath: body.rootPath,
      userId: body.userId,
      projectName: body.projectName,
      excludePatterns: body.excludePatterns || [
        'node_modules', '.git', '.next', 'dist', 'build', 
        '.vscode', 'coverage', '.nuxt', '.output', '__pycache__',
        '.env', '.env.local', '.env.production',
        'package-lock.json', 'yarn.lock', 'pnpm-lock.yaml'
      ],
      includeExtensions: body.includeExtensions || [
        '.ts', '.tsx', '.js', '.jsx', '.py', '.java', '.cpp', '.c',
        '.cs', '.go', '.rs', '.php', '.rb', '.swift', '.kt', '.scala',
        '.md', '.txt', '.json', '.yaml', '.yml'
      ],
      chunkSize: body.chunkSize || 1000,
      chunkOverlap: body.chunkOverlap || 200
    });

    if (result.success) {
      console.log(`✅ Successfully indexed codebase: ${result.totalFiles} files, ${result.totalChunks} chunks`);

      return NextResponse.json({
        success: true,
        message: 'Codebase indexed successfully using direct vector embedding',
        data: {
          documentId: result.documentId,
          totalFiles: result.totalFiles,
          totalChunks: result.totalChunks,
          chunkIds: result.chunkIds?.length || 0,
          method: 'direct_vector_embedding'
        }
      });
    } else {
      console.error('Failed to index codebase:', result.error);
      
      return NextResponse.json(
        { 
          success: false, 
          error: result.error || 'Failed to index codebase'
        },
        { status: 500 }
      );
    }

  } catch (error: any) {
    console.error('Error in codebase indexing API:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Internal server error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Codebase Indexing API',
    description: 'POST to this endpoint to index a codebase for semantic search',
    requiredFields: ['userId', 'rootPath', 'projectName'],
    optionalFields: ['excludePatterns', 'includeExtensions', 'chunkSize', 'chunkOverlap']
  });
}
