import { NextResponse } from 'next/server';
import { CodebaseDocumentationOrchestratorAgent } from '../../../../lib/agents/pmo/CodebaseDocumentationOrchestratorAgent';
import { createPMORecordFromForm, updatePMORecord } from '../../../../lib/firebase/pmoCollection';
import { addAgentOutput } from '../../../../lib/firebase/agentOutputs';
import { AgenticTeamId } from '../../../../lib/agents/pmo/PMOInterfaces';
import {
  CodebaseDocumentationRequestSchema,
  PMOFormInputSchema,
  CodebaseDocumentationRequest,
  PMOFormInput,
  safeValidateDocumentationRequest,
  safeValidatePMOFormInput,
  formatValidationErrors
} from '../../../../lib/schemas/codebaseDocumentationSchemas';

// Schemas are now imported from centralized schema file

/**
 * POST /api/codebase-documentation/stream
 * 
 * Processes codebase documentation requests with streaming updates
 */
export async function POST(request: Request) {
  try {
    const rawBody = await request.json();

    // Validate request using Zod schema
    const validationResult = safeValidateDocumentationRequest(rawBody);

    if (!validationResult.success) {
      console.error('Request validation failed:', validationResult.error.errors);
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: formatValidationErrors(validationResult.error)
        },
        { status: 400 }
      );
    }

    const body: CodebaseDocumentationRequest = validationResult.data;

    // Validate required fields
    if (!body.userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    if (!body.selectedPaths || body.selectedPaths.length === 0) {
      return NextResponse.json(
        { success: false, error: 'At least one codebase path must be selected' },
        { status: 400 }
      );
    }

    if (!body.description || body.description.trim().length === 0) {
      return NextResponse.json(
        { success: false, error: 'Description is required' },
        { status: 400 }
      );
    }

    // Create a readable stream for Server-Sent Events
    const stream = new ReadableStream({
      async start(controller) {
        const encoder = new TextEncoder();
        let isClosed = false;

        const sendUpdate = (data: any) => {
          if (isClosed) return;
          try {
            const message = `data: ${JSON.stringify(data)}\n\n`;
            controller.enqueue(encoder.encode(message));
          } catch (error) {
            console.warn('Failed to send update, controller may be closed:', error);
            isClosed = true;
          }
        };

        const closeController = () => {
          if (!isClosed) {
            isClosed = true;
            controller.close();
          }
        };

        try {
          // Send initial update
          sendUpdate({
            stage: 'initializing',
            message: 'Initializing documentation generation...',
            progress: 0
          });

          // Create PMO record
          sendUpdate({
            stage: 'creating-record',
            message: 'Creating project record...',
            progress: 10
          });

          // Generate comprehensive PMO assessment
          const pmoAssessment = `## PMO Assessment: Codebase Documentation Request

### Project Overview
**Request Type**: Comprehensive Codebase Documentation
**Requested By**: User ID ${body.userId}
**Date**: ${new Date().toISOString().split('T')[0]}
**Priority**: Medium

### User's Original Request
"${body.description}"

### Scope and Deliverables
**Target Codebase Paths**:
${body.selectedPaths.map(path => `- ${path}`).join('\n')}

**Documentation Scope**: ${body.documentationScope || 'Full comprehensive documentation'}
**Output Format**: ${body.outputFormat || 'Markdown with supporting artifacts'}

### Business Context
${body.customContext ? `**Additional Context**: ${body.customContext}` : '**Context**: Standard codebase documentation for development team knowledge transfer and project understanding.'}

### Expected Deliverables
1. **Comprehensive Product Overview** - Complete analysis of application features and capabilities
2. **Technical Architecture Documentation** - System design, patterns, and technical stack analysis
3. **User Configuration Guides** - Setup and configuration instructions
4. **Developer Documentation** - Plugin development guides and API documentation
5. **Installation & Setup Guides** - Complete deployment and setup procedures

### Team Assignment Recommendations
- **Codebase Documentation Team (Ag007)**: Specialized team responsible for comprehensive codebase analysis, technical documentation generation, architecture documentation, API documentation, and all aspects of codebase documentation workflows

### Success Criteria
- Documentation accurately reflects the codebase functionality as requested: "${body.description}"
- All selected paths are thoroughly analyzed and documented
- Documentation is actionable and provides clear value for intended audience
- Output format meets specified requirements and quality standards

### Resource Requirements
- Automated codebase analysis and documentation generation
- Multi-agent coordination for comprehensive coverage
- Quality assessment and iterative improvement process
- Final consolidation and formatting of deliverables`;

          const pmoFormInputRaw = {
            title: `Codebase Documentation: ${body.selectedPaths.slice(0, 3).join(', ')}${body.selectedPaths.length > 3 ? '...' : ''}`,
            description: body.description,
            priority: 'Medium' as const,
            category: 'Documentation',
            sourceFile: `Selected paths: ${body.selectedPaths.join(', ')}`,
            fileName: `Codebase Documentation - ${new Date().toISOString().split('T')[0]}`,
            customContext: body.customContext?.trim() || undefined,
            selectedFileId: undefined,
            selectedCategory: undefined,
            pmoAssessment: pmoAssessment
          };

          // Validate PMO form input using Zod schema
          const pmoValidationResult = safeValidatePMOFormInput(pmoFormInputRaw);

          if (!pmoValidationResult.success) {
            console.error('PMO form validation failed:', pmoValidationResult.error.errors);
            sendUpdate({
              stage: 'error',
              message: 'PMO record validation failed',
              progress: 0,
              error: 'Invalid PMO data: ' + formatValidationErrors(pmoValidationResult.error).map(e => e.message).join(', ')
            });
            return;
          }

          const pmoFormInput: PMOFormInput = pmoValidationResult.data;
          const pmoRecordId = await createPMORecordFromForm(body.userId, pmoFormInput);

          // Assign CodebaseDocumentation team for codebase documentation requests
          const assignedTeams = [
            AgenticTeamId.CodebaseDocumentation  // Specialized team for all codebase documentation work
          ];

          // Update PMO record with team assignments
          await updatePMORecord(body.userId, pmoRecordId, {
            agentIds: assignedTeams,
            teamSelectionRationale: "CodebaseDocumentation team assigned as the specialized team responsible for all codebase analysis, documentation generation, and technical documentation workflows. This team has the expertise and tools necessary to handle comprehensive codebase documentation requests efficiently."
          });

          sendUpdate({
            stage: 'record-created',
            message: 'Project record created with team assignments',
            progress: 20,
            data: { pmoRecordId, assignedTeams }
          });

          // Initialize the Codebase Documentation Orchestrator Agent with streaming
          const orchestratorAgent = new CodebaseDocumentationOrchestratorAgent({
            userId: body.userId,
            includeExplanation: true,
            streamResponse: true,
            onStreamUpdate: (update) => {
              // Map agent updates to our progress format
              const progressMap: { [key: string]: number } = {
                'analyzing-codebase': 30,
                'creating-dynamic-agents': 40,
                'processing-assignments': 60,
                'assessing-documentation': 75,
                'generating-additional-agents': 80,
                'consolidating-results': 90,
                'generating-final-docs': 95,
                'complete': 100
              };

              sendUpdate({
                stage: update.stage,
                message: update.message || `Processing ${update.stage.replace('-', ' ')}...`,
                progress: progressMap[update.stage] || 50,
                data: update.data,
                subAgentProgress: update.subAgentProgress,
                assessmentIteration: update.assessmentIteration
              });
            },
            codebasePaths: body.selectedPaths,
            documentationScope: body.documentationScope || 'full',
            outputFormat: body.outputFormat || 'markdown',
            includeArchitecture: body.includeArchitecture !== false,
            includeApiDocs: body.includeApiDocs !== false,
            includeDataFlow: body.includeDataFlow !== false,
            maxSubAgents: 9
          });

          // Process the documentation request
          const result = await orchestratorAgent.processDocumentationRequest(
            body.selectedPaths,
            body.description,
            body.customContext
          );

          if (!result.success) {
            sendUpdate({
              stage: 'error',
              message: result.error || 'Failed to generate documentation',
              progress: 0,
              error: result.error
            });
            closeController();
            return;
          }

          // Save the output to Firebase
          if (result.output) {
            await addAgentOutput({
              userId: body.userId,
              agentType: 'CodebaseDocumentationOrchestrator',
              title: `Codebase Documentation: ${body.selectedPaths.slice(0, 3).join(', ')}${body.selectedPaths.length > 3 ? '...' : ''}`,
              content: result.output,
              fileUrl: null,
              createdAt: new Date(),
              metadata: {
                pmoRecordId,
                selectedPaths: body.selectedPaths,
                documentationScope: body.documentationScope || 'full',
                outputFormat: body.outputFormat || 'markdown',
                outputDocumentIds: result.outputDocumentIds || [],
                taskId: result.taskId,
                subAgentResults: result.subAgentResults,
                codebaseMetrics: result.codebaseMetrics,
                documentationArtifacts: result.documentationArtifacts
              }
            });
          }

          // Update PMO record status to Completed
          await updatePMORecord(body.userId, pmoRecordId, {
            status: 'Completed'
          });

          // Send final completion update
          sendUpdate({
            stage: 'complete',
            message: 'Documentation generation completed successfully!',
            progress: 100,
            data: {
              pmoRecordId,
              outputDocumentIds: result.outputDocumentIds,
              success: true
            }
          });

          closeController();

        } catch (error: any) {
          console.error('Streaming documentation generation error:', error);
          sendUpdate({
            stage: 'error',
            message: error.message || 'An unexpected error occurred',
            progress: 0,
            error: error.message
          });
          closeController();
        }
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error: any) {
    console.error('Codebase documentation streaming API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to process codebase documentation request' 
      },
      { status: 500 }
    );
  }
}
