/**
 * Comprehensive Zod schemas for codebase documentation system
 * Provides type safety, validation, and error handling for all data structures
 */

import { z } from 'zod';

// Base validation schemas
export const NonEmptyStringSchema = z.string().min(1, "Value cannot be empty");
export const PositiveNumberSchema = z.number().min(0, "Value must be non-negative");
export const EmailSchema = z.string().email("Invalid email format");

// Priority and complexity enums
export const PrioritySchema = z.enum(['Low', 'Medium', 'High']);
export const ComplexitySchema = z.enum(['low', 'medium', 'high']);
export const DocumentationScopeSchema = z.enum(['full', 'partial', 'specific']);
export const OutputFormatSchema = z.enum(['markdown', 'html', 'pdf']);

// Core codebase analysis schema
export const CodebaseAnalysisResultSchema = z.object({
  totalFiles: PositiveNumberSchema,
  totalLines: PositiveNumberSchema,
  languages: z.array(NonEmptyStringSchema).default([]),
  complexity: ComplexitySchema.default('low'),
  mainDirectories: z.array(NonEmptyStringSchema).default([]),
  keyFiles: z.array(NonEmptyStringSchema).default([]),
  frameworks: z.array(NonEmptyStringSchema).default([]),
  dependencies: z.array(NonEmptyStringSchema).default([])
});

// Sub-agent related schemas
export const SubAgentAssignmentSchema = z.object({
  agentId: NonEmptyStringSchema,
  agentName: NonEmptyStringSchema,
  assignment: z.string().min(10, "Assignment description must be at least 10 characters"),
  priority: z.enum(['high', 'medium', 'low']),
  estimatedComplexity: z.enum(['simple', 'moderate', 'complex']),
  requiredPaths: z.array(NonEmptyStringSchema),
  specialization: NonEmptyStringSchema
});

export const SubAgentResultSchema = z.object({
  agentId: NonEmptyStringSchema,
  agentName: NonEmptyStringSchema,
  assignment: NonEmptyStringSchema,
  output: z.string().min(1, "Output cannot be empty"),
  success: z.boolean(),
  error: z.string().optional(),
  artifacts: z.object({
    diagrams: z.array(NonEmptyStringSchema).optional(),
    codeSnippets: z.array(NonEmptyStringSchema).optional(),
    configurations: z.array(NonEmptyStringSchema).optional()
  }).optional()
});

// Dynamic sub-agent schema
export const DynamicSubAgentSchema = z.object({
  id: NonEmptyStringSchema,
  name: NonEmptyStringSchema,
  description: z.string().min(10, "Description must be at least 10 characters"),
  specialization: NonEmptyStringSchema,
  capabilities: z.array(NonEmptyStringSchema).min(1, "At least one capability is required")
});

// Documentation assessment schema
export const DocumentationAssessmentSchema = z.object({
  isComplete: z.boolean(),
  completionScore: z.number().min(0).max(100),
  missingAreas: z.array(NonEmptyStringSchema),
  qualityIssues: z.array(NonEmptyStringSchema),
  recommendedAdditionalAgents: z.array(DynamicSubAgentSchema),
  overallFeedback: NonEmptyStringSchema
});

// Chart recommendation schema
export const ChartRecommendationSchema = z.object({
  description: z.string().min(5, "Chart description must be at least 5 characters"),
  type: z.enum(['bar', 'line', 'pie', 'area', 'scatter', 'radar', 'composed', 'table', 'flow', 'heatmap', 'bubble']),
  rationale: z.string().min(10, "Chart rationale must be at least 10 characters")
});

// API request schemas
export const CodebaseDocumentationRequestSchema = z.object({
  userId: NonEmptyStringSchema,
  selectedPaths: z.array(NonEmptyStringSchema).min(1, "At least one path must be selected"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  customContext: z.string().optional(),
  documentationScope: DocumentationScopeSchema.default('full'),
  outputFormat: OutputFormatSchema.default('markdown'),
  includeArchitecture: z.boolean().default(true),
  includeApiDocs: z.boolean().default(true),
  includeDataFlow: z.boolean().default(true)
});

// PMO form input schema
export const PMOFormInputSchema = z.object({
  title: z.string().min(5, "Title must be at least 5 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  priority: PrioritySchema.default('Medium'),
  category: NonEmptyStringSchema.default('Documentation'),
  sourceFile: z.string().optional(),
  fileName: z.string().optional(),
  customContext: z.string().optional(),
  selectedFileId: z.string().optional(),
  selectedCategory: z.string().optional(),
  pmoAssessment: z.string().min(50, "PMO assessment must be comprehensive")
});

// Orchestrator options schema
export const CodebaseDocumentationOrchestratorOptionsSchema = z.object({
  userId: NonEmptyStringSchema,
  includeExplanation: z.boolean().optional(),
  streamResponse: z.boolean().optional(),
  codebasePaths: z.array(NonEmptyStringSchema).optional(),
  documentationScope: DocumentationScopeSchema.optional(),
  outputFormat: OutputFormatSchema.optional(),
  includeArchitecture: z.boolean().optional(),
  includeApiDocs: z.boolean().optional(),
  includeDataFlow: z.boolean().optional(),
  maxSubAgents: z.number().min(1).max(20).optional()
});

// Stream update schema
export const StreamUpdateSchema = z.object({
  stage: NonEmptyStringSchema,
  message: NonEmptyStringSchema,
  progress: z.number().min(0).max(100),
  data: z.record(z.any()).optional(),
  error: z.string().optional()
});

// Team assignment schema
export const TeamAssignmentSchema = z.object({
  teamId: NonEmptyStringSchema,
  teamName: NonEmptyStringSchema,
  rationale: z.string().min(10, "Team assignment rationale must be at least 10 characters"),
  capabilities: z.array(NonEmptyStringSchema)
});

// Type inference from schemas
export type CodebaseAnalysisResult = z.infer<typeof CodebaseAnalysisResultSchema>;
export type SubAgentAssignment = z.infer<typeof SubAgentAssignmentSchema>;
export type SubAgentResult = z.infer<typeof SubAgentResultSchema>;
export type DynamicSubAgent = z.infer<typeof DynamicSubAgentSchema>;
export type DocumentationAssessment = z.infer<typeof DocumentationAssessmentSchema>;
export type ChartRecommendation = z.infer<typeof ChartRecommendationSchema>;
export type CodebaseDocumentationRequest = z.infer<typeof CodebaseDocumentationRequestSchema>;
export type PMOFormInput = z.infer<typeof PMOFormInputSchema>;
export type CodebaseDocumentationOrchestratorOptions = z.infer<typeof CodebaseDocumentationOrchestratorOptionsSchema>;
export type StreamUpdate = z.infer<typeof StreamUpdateSchema>;
export type TeamAssignment = z.infer<typeof TeamAssignmentSchema>;

// Validation utility functions
export const validateCodebaseAnalysis = (data: unknown): CodebaseAnalysisResult => {
  return CodebaseAnalysisResultSchema.parse(data);
};

export const validateSubAgentAssignment = (data: unknown): SubAgentAssignment => {
  return SubAgentAssignmentSchema.parse(data);
};

export const validateSubAgentResult = (data: unknown): SubAgentResult => {
  return SubAgentResultSchema.parse(data);
};

export const validateDocumentationRequest = (data: unknown): CodebaseDocumentationRequest => {
  return CodebaseDocumentationRequestSchema.parse(data);
};

export const validatePMOFormInput = (data: unknown): PMOFormInput => {
  return PMOFormInputSchema.parse(data);
};

// Safe validation functions that return results with error handling
export const safeValidateCodebaseAnalysis = (data: unknown) => {
  return CodebaseAnalysisResultSchema.safeParse(data);
};

export const safeValidateSubAgentAssignment = (data: unknown) => {
  return SubAgentAssignmentSchema.safeParse(data);
};

export const safeValidateSubAgentResult = (data: unknown) => {
  return SubAgentResultSchema.safeParse(data);
};

export const safeValidateDocumentationRequest = (data: unknown) => {
  return CodebaseDocumentationRequestSchema.safeParse(data);
};

export const safeValidatePMOFormInput = (data: unknown) => {
  return PMOFormInputSchema.safeParse(data);
};

// Error formatting utility
export const formatValidationErrors = (errors: z.ZodError) => {
  return errors.errors.map(err => ({
    field: err.path.join('.'),
    message: err.message,
    code: err.code
  }));
};

// Default values for common schemas
export const getDefaultCodebaseAnalysis = (): CodebaseAnalysisResult => {
  return CodebaseAnalysisResultSchema.parse({
    totalFiles: 0,
    totalLines: 0,
    languages: [],
    complexity: 'low',
    mainDirectories: [],
    keyFiles: [],
    frameworks: [],
    dependencies: []
  });
};

export const getDefaultDocumentationRequest = (userId: string, paths: string[], description: string): CodebaseDocumentationRequest => {
  return CodebaseDocumentationRequestSchema.parse({
    userId,
    selectedPaths: paths,
    description,
    documentationScope: 'full',
    outputFormat: 'markdown',
    includeArchitecture: true,
    includeApiDocs: true,
    includeDataFlow: true
  });
};
