/**
 * Codebase Documentation Orchestrator Agent
 * 
 * This agent coordinates comprehensive documentation generation for codebases
 * by analyzing structure and deploying specialized sub-agents for different
 * aspects of documentation.
 */

import { processWithAnthropic } from '../../tools/anthropic-ai';
import { processWithGoogleAI } from '../../tools/google-ai';
import {
  CodebaseDocumentationTeamAgentResult
} from './TeamAgentInterfaces';
import { v4 as uuidv4 } from 'uuid';
import { ChartTool, ChartGenerationResult } from '../../tools/chart-tool';
import { z } from 'zod';
import { promises as fs } from 'fs';
import path from 'path';
import { queryDocumentsTool } from '../../tools/queryDocumentsTool';
import { codebaseIndexingTool } from '../../tools/codebase-indexing-tool';
import {
  CodebaseAnalysisResultSchema,
  SubAgentAssignmentSchema,
  SubAgentResultSchema,
  DocumentationAssessmentSchema,
  DynamicSubAgentSchema,
  ChartRecommendationSchema,
  CodebaseDocumentationOrchestratorOptionsSchema,
  validateCodebaseAnalysis,
  validateSubAgentAssignment,
  validateSubAgentResult,
  safeValidateCodebaseAnalysis,
  safeValidateSubAgentAssignment,
  safeValidateSubAgentResult,
  formatValidationErrors,
  getDefaultCodebaseAnalysis
} from '../../schemas/codebaseDocumentationSchemas';

// Custom options interface for Codebase Documentation Orchestrator
export interface CodebaseDocumentationOrchestratorOptions {
  userId: string;
  includeExplanation?: boolean;
  streamResponse?: boolean;
  onStreamUpdate?: (update: CodebaseDocumentationStreamUpdate) => void;
  codebasePaths?: string[];
  documentationScope?: 'full' | 'partial' | 'specific';
  outputFormat?: 'markdown' | 'html' | 'pdf';
  includeArchitecture?: boolean;
  includeApiDocs?: boolean;
  includeDataFlow?: boolean;
  maxSubAgents?: number;
}

// Dynamic sub-agent definition for flexible documentation tasks
export interface DynamicSubAgent {
  id: string;
  name: string;
  description: string;
  specialization: string;
  capabilities: string[];
}

export interface SubAgentAssignment {
  agentId: string;
  agentName: string;
  assignment: string;
  priority: 'high' | 'medium' | 'low';
  estimatedComplexity: 'simple' | 'moderate' | 'complex';
  requiredPaths: string[];
  specialization: string;
}

export interface SubAgentResult {
  agentId: string;
  agentName: string;
  assignment: string;
  output: string;
  success: boolean;
  error?: string;
  artifacts?: {
    diagrams?: string[];
    codeSnippets?: string[];
    configurations?: string[];
  };
}

// Documentation assessment interface
export interface DocumentationAssessment {
  isComplete: boolean;
  completionScore: number; // 0-100
  missingAreas: string[];
  qualityIssues: string[];
  recommendedAdditionalAgents: DynamicSubAgent[];
  overallFeedback: string;
}

// Zod schemas are now imported from centralized schema file

// All schemas are now imported from centralized schema file
// Type inference from imported schemas
export type DynamicSubAgentType = z.infer<typeof DynamicSubAgentSchema>;
export type SubAgentAssignmentType = z.infer<typeof SubAgentAssignmentSchema>;
export type DocumentationAssessmentType = z.infer<typeof DocumentationAssessmentSchema>;
export type ChartRecommendationType = z.infer<typeof ChartRecommendationSchema>;
export type SubAgentResultType = z.infer<typeof SubAgentResultSchema>;
export type CodebaseAnalysisResultType = z.infer<typeof CodebaseAnalysisResultSchema>;
export type CodebaseDocumentationOrchestratorOptionsType = z.infer<typeof CodebaseDocumentationOrchestratorOptionsSchema>;

export interface CodebaseAnalysisResult {
  totalFiles: number;
  totalLines: number;
  languages: string[];
  complexity: 'low' | 'medium' | 'high';
  mainDirectories: string[];
  keyFiles: string[];
  frameworks: string[];
  dependencies: string[];
}

export interface CodebaseDocumentationStreamUpdate {
  stage: 'analyzing-codebase' | 'creating-dynamic-agents' | 'processing-assignments' | 'assessing-documentation' | 'generating-additional-agents' | 'consolidating-results' | 'generating-final-docs' | 'complete';
  data?: any;
  message?: string;
  subAgentProgress?: {
    completed: number;
    total: number;
    currentAgent?: string;
  };
  assessmentIteration?: number;
}

export class CodebaseDocumentationOrchestratorAgent {
  private options: CodebaseDocumentationOrchestratorOptions;
  private subAgentResults: Map<string, SubAgentResult> = new Map();
  private chartTool: ChartTool = new ChartTool();
  private maxAssessmentIterations: number = 3;
  private codebaseDocumentId: string | null = null;

  constructor(options: CodebaseDocumentationOrchestratorOptions) {
    this.options = {
      userId: options.userId,
      includeExplanation: options.includeExplanation || false,
      streamResponse: options.streamResponse || false,
      onStreamUpdate: options.onStreamUpdate,
      codebasePaths: options.codebasePaths || [],
      documentationScope: options.documentationScope || 'full',
      outputFormat: options.outputFormat || 'markdown',
      includeArchitecture: options.includeArchitecture !== false,
      includeApiDocs: options.includeApiDocs !== false,
      includeDataFlow: options.includeDataFlow !== false,
      maxSubAgents: options.maxSubAgents || 9
    };

    if (!this.options.userId) {
      throw new Error("CodebaseDocumentationOrchestratorAgent requires a userId in options.");
    }
  }

  /**
   * Process codebase documentation request with dynamic sub-agent creation and assessment
   */
  public async processDocumentationRequest(
    selectedPaths: string[],
    description: string,
    customContext?: string
  ): Promise<CodebaseDocumentationTeamAgentResult> {
    try {
      this._updateStream('analyzing-codebase', {}, 'Analyzing codebase structure and complexity...');

      // Step 1: Analyze the codebase
      const codebaseAnalysis = await this._analyzeCodebase(selectedPaths);

      // Step 1.5: Index the codebase for semantic search
      this._updateStream('analyzing-codebase', {}, 'Indexing codebase for semantic search...');
      await this._indexCodebaseForRAG(selectedPaths, description);

      this._updateStream('creating-dynamic-agents', { codebaseAnalysis }, 'Creating dynamic sub-agents based on requirements...');

      // Step 2: Dynamically create initial sub-agents based on request analysis
      let currentSubAgents = await this._createDynamicSubAgents(
        selectedPaths,
        description,
        codebaseAnalysis,
        customContext
      );

      let assessmentIteration = 0;
      let isDocumentationComplete = false;
      // Clear any previous results and use instance property
      this.subAgentResults.clear();

      // Step 3: Iterative documentation generation and assessment
      while (!isDocumentationComplete && assessmentIteration < this.maxAssessmentIterations) {
        assessmentIteration++;

        this._updateStream('processing-assignments', {
          subAgentProgress: { completed: 0, total: currentSubAgents.length },
          assessmentIteration
        }, `Processing sub-agent assignments (iteration ${assessmentIteration})...`);

        // Execute current batch of sub-agents
        const batchResults = await this._executeSubAgentAssignments(currentSubAgents, description);

        // Merge results with previous iterations
        batchResults.forEach((result, agentId) => {
          this.subAgentResults.set(agentId, result);
        });

        this._updateStream('assessing-documentation', {
          completedAgents: this.subAgentResults.size,
          assessmentIteration
        }, `Assessing documentation completeness (iteration ${assessmentIteration})...`);

        // Step 4: Assess documentation completeness
        const assessment = await this._assessDocumentationCompleteness(
          this.subAgentResults,
          description,
          codebaseAnalysis,
          selectedPaths
        );

        isDocumentationComplete = assessment.isComplete;

        if (!isDocumentationComplete && assessmentIteration < this.maxAssessmentIterations) {
          this._updateStream('generating-additional-agents', {
            missingAreas: assessment.missingAreas,
            recommendedAgents: assessment.recommendedAdditionalAgents.length,
            assessmentIteration
          }, `Creating additional sub-agents for missing areas...`);

          // Create additional sub-agents for missing areas
          currentSubAgents = assessment.recommendedAdditionalAgents.map(agent => ({
            agentId: agent.id,
            agentName: agent.name,
            assignment: `Address missing documentation area: ${agent.description}`,
            priority: 'high' as const,
            estimatedComplexity: 'moderate' as const,
            requiredPaths: selectedPaths,
            specialization: agent.specialization
          }));
        }
      }

      this._updateStream('consolidating-results', {
        totalAgents: this.subAgentResults.size,
        iterations: assessmentIteration
      }, 'Consolidating documentation results...');

      // Step 5: Generate final consolidated documentation
      const documentationArtifacts = await this._consolidateDocumentation(
        this.subAgentResults,
        codebaseAnalysis,
        description
      );

      this._updateStream('generating-final-docs', { documentationArtifacts }, 'Generating final documentation and charts...');

      // Step 6: Generate supporting charts dynamically based on content
      const supportingCharts = await this._generateDynamicDocumentationCharts(
        codebaseAnalysis,
        documentationArtifacts,
        this.subAgentResults
      );

      this._updateStream('complete', {}, 'Documentation generation complete!');

      return {
        success: true,
        taskId: uuidv4(),
        output: this._formatFinalOutput(documentationArtifacts, codebaseAnalysis),
        outputDocumentIds: [], // Will be populated when saved to Firebase
        documentationArtifacts,
        supportingCharts,
        subAgentResults: Array.from(this.subAgentResults.values()).map(result => ({
          agentType: result.agentName, // Use agentName for backward compatibility
          assignment: result.assignment,
          output: result.output,
          success: result.success,
          error: result.error ? { error: result.error } : { error: null }
        })),
        codebaseMetrics: codebaseAnalysis
      };

    } catch (error: any) {
      console.error('CodebaseDocumentationOrchestratorAgent: Error processing request:', error);
      return {
        success: false,
        taskId: uuidv4(),
        output: '',
        outputDocumentIds: [],
        error: error.message || 'Failed to process codebase documentation request'
      };
    }
  }

  /**
   * Checks if a path should be restricted from analysis for security reasons.
   * Prevents access to system directories and sensitive locations.
   */
  private _isRestrictedPath(safePath: string): boolean {
    const normalizedPath = safePath.toLowerCase().replace(/\\/g, '/');

    // Restrict system directories
    const restrictedPaths = [
      'c:/windows',
      'c:/program files',
      'c:/program files (x86)',
      'c:/programdata',
      'c:/users/<USER>',
      'c:/users/<USER>',
      'c:/users/<USER>',
      '/system',
      '/usr/bin',
      '/usr/sbin',
      '/bin',
      '/sbin',
      '/etc',
      '/var/log',
      '/proc',
      '/sys'
    ];

    // Check if path starts with any restricted directory
    for (const restrictedPath of restrictedPaths) {
      if (normalizedPath.startsWith(restrictedPath)) {
        return true;
      }
    }

    // Allow access to user directories and development folders
    return false;
  }

  /**
   * Analyze codebase structure and complexity
   */
  private async _analyzeCodebase(selectedPaths: string[]): Promise<CodebaseAnalysisResult> {
    const analysis = {
      totalFiles: 0,
      totalLines: 0,
      languages: [] as string[],
      complexity: 'low' as 'low' | 'medium' | 'high',
      mainDirectories: [] as string[],
      keyFiles: [] as string[],
      frameworks: [] as string[],
      dependencies: [] as string[]
    };

    const processedFiles = new Set<string>();
    const languageCount: { [key: string]: number } = {};
    const dependenciesSet = new Set<string>();
    const frameworkIndicators = new Map<string, string[]>([
      ['React', ['react', 'jsx', 'tsx', 'react-dom']],
      ['Next.js', ['next', 'next.config.js', 'next.config.ts']],
      ['Vue.js', ['vue', '.vue', 'vue-router']],
      ['Angular', ['@angular', 'angular.json', '.component.ts']],
      ['Express', ['express', 'app.js', 'server.js']],
      ['Django', ['django', 'manage.py', 'settings.py']],
      ['Flask', ['flask', 'app.py']],
      ['Spring Boot', ['spring-boot', 'application.properties', 'pom.xml']],
      ['Laravel', ['laravel', 'artisan', 'composer.json']],
      ['Rails', ['rails', 'Gemfile', 'config/routes.rb']]
    ]);

    // Process each selected path
    for (const inputPath of selectedPaths) {
      try {
        const safePath = path.resolve(inputPath);

        // Security check - ensure path is accessible and not a system directory
        if (this._isRestrictedPath(safePath)) {
          console.warn(`Restricted path, skipping: ${inputPath}`);
          continue;
        }

        await this._analyzePathRecursively(
          safePath,
          analysis,
          processedFiles,
          languageCount,
          dependenciesSet
        );
      } catch (error) {
        console.warn(`Error analyzing path ${inputPath}:`, error);
        // Skip inaccessible paths instead of providing mock data
        continue;
      }
    }

    // Convert language counts to sorted array
    analysis.languages = Object.keys(languageCount)
      .sort((a, b) => languageCount[b] - languageCount[a]);

    // Convert dependencies set to array
    analysis.dependencies = Array.from(dependenciesSet);

    // Detect frameworks
    analysis.frameworks = this._detectFrameworks(analysis, frameworkIndicators);

    // Calculate complexity
    analysis.complexity = this._calculateComplexity(analysis);

    // Validate the analysis result using Zod schema
    try {
      const validatedAnalysis = CodebaseAnalysisResultSchema.parse(analysis);
      return validatedAnalysis;
    } catch (validationError) {
      console.warn('Codebase analysis validation failed, using defaults:', validationError);
      // Return a safe default analysis if validation fails
      return CodebaseAnalysisResultSchema.parse({
        totalFiles: 0,
        totalLines: 0,
        languages: [],
        complexity: 'low',
        mainDirectories: [],
        keyFiles: [],
        frameworks: [],
        dependencies: []
      });
    }
  }

  /**
   * Recursively analyze a path for files and directories
   */
  private async _analyzePathRecursively(
    currentPath: string,
    analysis: CodebaseAnalysisResult,
    processedFiles: Set<string>,
    languageCount: { [key: string]: number },
    dependenciesSet: Set<string>
  ): Promise<void> {
    try {
      const stats = await fs.stat(currentPath);

      if (stats.isDirectory()) {
        // Add to main directories if it's a top-level directory
        const dirName = path.basename(currentPath);
        if (!analysis.mainDirectories.includes(dirName)) {
          analysis.mainDirectories.push(dirName);
        }

        // Skip certain directories
        const skipDirs = ['node_modules', '.git', '.next', 'dist', 'build', '.vscode', 'coverage', '.nuxt', '.output'];
        if (skipDirs.includes(dirName)) {
          return;
        }

        const items = await fs.readdir(currentPath);
        for (const item of items) {
          const itemPath = path.join(currentPath, item);
          await this._analyzePathRecursively(itemPath, analysis, processedFiles, languageCount, dependenciesSet);
        }
      } else {
        // Process file
        if (processedFiles.has(currentPath)) {
          return;
        }
        processedFiles.add(currentPath);

        analysis.totalFiles++;
        const ext = path.extname(currentPath).toLowerCase();
        const fileName = path.basename(currentPath);

        // Count language files
        const language = this._getLanguageFromExtension(ext);
        if (language) {
          languageCount[language] = (languageCount[language] || 0) + 1;
        }

        // Track key configuration files
        if (this._isKeyFile(fileName)) {
          analysis.keyFiles.push(fileName);
        }

        // Extract dependencies from package files
        if (fileName === 'package.json' || fileName === 'requirements.txt' || fileName === 'Gemfile' || fileName === 'composer.json') {
          try {
            const content = await fs.readFile(currentPath, 'utf-8');
            const deps = this._extractDependencies(fileName, content);
            deps.forEach((dep: string) => dependenciesSet.add(dep));
          } catch (error) {
            // Ignore read errors
          }
        }

        // Count lines for text files
        if (this._isTextFile(ext)) {
          try {
            const content = await fs.readFile(currentPath, 'utf-8');
            const lines = content.split('\n').length;
            analysis.totalLines += lines;
          } catch (error) {
            // File might be binary or unreadable
          }
        }
      }
    } catch (error) {
      // Skip files/directories that can't be accessed
    }
  }

  /**
   * Get programming language from file extension
   */
  private _getLanguageFromExtension(ext: string): string | null {
    const languageMap: { [key: string]: string } = {
      '.js': 'JavaScript',
      '.jsx': 'JavaScript',
      '.ts': 'TypeScript',
      '.tsx': 'TypeScript',
      '.py': 'Python',
      '.java': 'Java',
      '.cpp': 'C++',
      '.c': 'C',
      '.cs': 'C#',
      '.php': 'PHP',
      '.rb': 'Ruby',
      '.go': 'Go',
      '.rs': 'Rust',
      '.swift': 'Swift',
      '.kt': 'Kotlin',
      '.scala': 'Scala',
      '.html': 'HTML',
      '.css': 'CSS',
      '.scss': 'SCSS',
      '.sass': 'Sass',
      '.less': 'Less',
      '.vue': 'Vue',
      '.svelte': 'Svelte',
      '.sql': 'SQL',
      '.sh': 'Shell',
      '.bash': 'Bash',
      '.ps1': 'PowerShell',
      '.yaml': 'YAML',
      '.yml': 'YAML',
      '.json': 'JSON',
      '.xml': 'XML',
      '.md': 'Markdown',
      '.dockerfile': 'Docker'
    };

    return languageMap[ext] || null;
  }

  /**
   * Check if file is a key configuration file
   */
  private _isKeyFile(fileName: string): boolean {
    const keyFiles = [
      'package.json', 'tsconfig.json', 'webpack.config.js', 'next.config.js',
      'tailwind.config.js', 'postcss.config.js', '.env', '.env.local',
      'docker-compose.yml', 'Dockerfile', '.gitignore', '.eslintrc',
      'prettier.config.js', 'jest.config.js', 'babel.config.js',
      'angular.json', 'vue.config.js', 'nuxt.config.js', 'gatsby-config.js',
      'rollup.config.js', 'vite.config.js', 'svelte.config.js'
    ];

    return keyFiles.includes(fileName) || fileName.startsWith('.env');
  }

  /**
   * Check if file is a text file that should be analyzed for lines
   */
  private _isTextFile(ext: string): boolean {
    const textExtensions = [
      '.js', '.jsx', '.ts', '.tsx', '.py', '.java', '.cpp', '.c', '.cs',
      '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala', '.html',
      '.css', '.scss', '.sass', '.less', '.vue', '.svelte', '.sql',
      '.sh', '.bash', '.ps1', '.yaml', '.yml', '.json', '.xml', '.md',
      '.txt', '.log', '.config'
    ];

    return textExtensions.includes(ext);
  }

  /**
   * Extract dependencies from package files
   */
  private _extractDependencies(fileName: string, content: string): string[] {
    const deps: string[] = [];

    try {
      if (fileName === 'package.json') {
        const pkg = JSON.parse(content);
        if (pkg.dependencies) {
          deps.push(...Object.keys(pkg.dependencies));
        }
        if (pkg.devDependencies) {
          deps.push(...Object.keys(pkg.devDependencies));
        }
      } else if (fileName === 'requirements.txt') {
        const lines = content.split('\n');
        for (const line of lines) {
          const trimmed = line.trim();
          if (trimmed && !trimmed.startsWith('#')) {
            const dep = trimmed.split('==')[0].split('>=')[0].split('<=')[0].trim();
            if (dep) deps.push(dep);
          }
        }
      } else if (fileName === 'Gemfile') {
        const lines = content.split('\n');
        for (const line of lines) {
          const trimmed = line.trim();
          const gemMatch = trimmed.match(/gem\s+['"]([^'"]+)['"]/);
          if (gemMatch) {
            deps.push(gemMatch[1]);
          }
        }
      } else if (fileName === 'composer.json') {
        const pkg = JSON.parse(content);
        if (pkg.require) {
          deps.push(...Object.keys(pkg.require));
        }
        if (pkg['require-dev']) {
          deps.push(...Object.keys(pkg['require-dev']));
        }
      }
    } catch (error) {
      // Ignore parsing errors
    }

    return deps;
  }

  /**
   * Detect frameworks based on analysis
   */
  private _detectFrameworks(
    analysis: CodebaseAnalysisResult,
    frameworkIndicators: Map<string, string[]>
  ): string[] {
    const detectedFrameworks: string[] = [];

    for (const [framework, indicators] of frameworkIndicators) {
      const hasIndicator = indicators.some(indicator =>
        analysis.dependencies.includes(indicator) ||
        analysis.keyFiles.includes(indicator) ||
        analysis.keyFiles.some(file => file.includes(indicator))
      );

      if (hasIndicator) {
        detectedFrameworks.push(framework);
      }
    }

    return detectedFrameworks;
  }



  /**
   * Calculate codebase complexity based on metrics
   */
  private _calculateComplexity(analysis: CodebaseAnalysisResult): 'low' | 'medium' | 'high' {
    const fileCount = analysis.totalFiles;
    const languageCount = analysis.languages.length;
    const dependencyCount = analysis.dependencies.length;

    if (fileCount > 500 || languageCount > 5 || dependencyCount > 50) {
      return 'high';
    } else if (fileCount > 100 || languageCount > 3 || dependencyCount > 20) {
      return 'medium';
    } else {
      return 'low';
    }
  }

  /**
   * Dynamically create sub-agents based on documentation requirements
   */
  private async _createDynamicSubAgents(
    selectedPaths: string[],
    description: string,
    codebaseAnalysis: CodebaseAnalysisResult,
    customContext?: string
  ): Promise<SubAgentAssignment[]> {
    const prompt = `
You are a Documentation Strategy AI. Based on the user's documentation request and codebase analysis,
dynamically determine which specialized sub-agents are needed to fulfill the documentation requirements.

CODEBASE ANALYSIS:
- Total Files: ${codebaseAnalysis.totalFiles}
- Languages: ${codebaseAnalysis.languages.join(', ')}
- Complexity: ${codebaseAnalysis.complexity}
- Frameworks: ${codebaseAnalysis.frameworks.join(', ')}
- Main Directories: ${codebaseAnalysis.mainDirectories.join(', ')}

USER DOCUMENTATION REQUEST:
${description}

SELECTED PATHS:
${selectedPaths.join('\n')}

${customContext ? `ADDITIONAL CONTEXT:\n${customContext}` : ''}

Based on this information, create a list of specialized sub-agents that are specifically needed for this documentation request.
Do NOT create a fixed set of agents - only create agents that are relevant to the specific request and codebase.

IMPORTANT: You must respond with ONLY a valid JSON array. Do not include any explanatory text, markdown formatting, or code blocks.

Return a JSON array where each object has this exact structure:
[
  {
    "agentId": "unique-agent-id",
    "agentName": "Descriptive Agent Name",
    "assignment": "Specific task description for this documentation request",
    "priority": "high",
    "estimatedComplexity": "moderate",
    "requiredPaths": ["relevant", "paths", "only"],
    "specialization": "Area of expertise"
  }
]

IMPORTANT VALIDATION REQUIREMENTS:
- priority must be exactly one of: "high", "medium", "low"
- estimatedComplexity must be exactly one of: "simple", "moderate", "complex"
- Do NOT use "high" for estimatedComplexity - use "complex" instead
- agentId must be unique and non-empty
- assignment must be at least 10 characters long

Focus on creating only the agents that are actually needed for this specific request.
Respond with ONLY the JSON array, no other text.
`;

    try {
      const result = await processWithAnthropic({
        prompt,
        model: "claude-sonnet-4-20250514",
        modelOptions: {
          temperature: 0.3,
          maxTokens: 3000
        }
      });

      console.log('Raw Claude response for dynamic sub-agents:', result.substring(0, 200) + '...');

      // Clean the result by removing markdown code blocks and extracting JSON
      let cleanedResult = result.trim();

      // Remove markdown code blocks
      if (cleanedResult.startsWith('```json')) {
        cleanedResult = cleanedResult.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanedResult.startsWith('```')) {
        cleanedResult = cleanedResult.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      // If the response contains explanatory text, try to extract just the JSON array
      const jsonArrayMatch = cleanedResult.match(/\[[\s\S]*\]/);
      if (jsonArrayMatch) {
        cleanedResult = jsonArrayMatch[0];
      }

      // Additional cleanup for common issues
      cleanedResult = cleanedResult.trim();

      console.log('Cleaned result for JSON parsing:', cleanedResult.substring(0, 200) + '...');

      const rawAssignments = JSON.parse(cleanedResult);

      // Validate and parse assignments using Zod
      const validatedAssignments: SubAgentAssignment[] = [];
      for (const assignment of rawAssignments) {
        try {
          // Fix common LLM mistakes before validation
          console.log('Original assignment before fix:', assignment);
          const fixedAssignment = this._fixAssignmentValues(assignment);
          console.log('Fixed assignment after fix:', fixedAssignment);
          const validated = SubAgentAssignmentSchema.parse(fixedAssignment);
          validatedAssignments.push(validated);
        } catch (validationError) {
          console.warn('Invalid assignment from LLM, skipping:', assignment, validationError);
          // Try to create a fallback assignment
          const fallbackAssignment = this._createFallbackAssignment(assignment, selectedPaths);
          if (fallbackAssignment) {
            validatedAssignments.push(fallbackAssignment);
          }
        }
      }

      if (validatedAssignments.length === 0) {
        console.warn('No valid assignments from LLM, using fallback');
        return this._createFallbackAssignments(selectedPaths);
      }

      return validatedAssignments.slice(0, this.options.maxSubAgents || 9);
    } catch (error: any) {
      console.error('Failed to create dynamic sub-agent assignments with Claude, using fallback:', error);
      console.error('Error details:', {
        message: error.message,
        stack: error.stack?.substring(0, 500)
      });
      return this._createFallbackAssignments(selectedPaths);
    }
  }

  /**
   * Assess documentation completeness using Google AI
   */
  private async _assessDocumentationCompleteness(
    subAgentResults: Map<string, SubAgentResult>,
    originalDescription: string,
    codebaseAnalysis: CodebaseAnalysisResult,
    selectedPaths: string[]
  ): Promise<DocumentationAssessment> {
    const completedDocumentation = Array.from(subAgentResults.values())
      .filter(result => result.success)
      .map(result => `**${result.agentName}**: ${result.output.substring(0, 500)}...`)
      .join('\n\n');

    const prompt = `
You are a Documentation Assessment AI using Google's Gemini model. Your primary task is to evaluate how well the generated documentation fulfills the USER'S SPECIFIC REQUEST.

🎯 USER'S ORIGINAL REQUEST:
"${originalDescription}"

📁 TARGET CODEBASE PATHS:
${selectedPaths.join('\n')}

📊 CODEBASE ANALYSIS RESULTS:
- Total Files: ${codebaseAnalysis.totalFiles}
- Languages: ${codebaseAnalysis.languages.join(', ')}
- Complexity: ${codebaseAnalysis.complexity}
- Frameworks: ${codebaseAnalysis.frameworks.join(', ')}

📝 GENERATED DOCUMENTATION:
${completedDocumentation}

🔍 ASSESSMENT CRITERIA:
Your evaluation must focus on whether the documentation addresses the user's specific request: "${originalDescription}"

For the CastMate application analysis, specifically evaluate if the documentation covers:
- Comprehensive product overview as requested
- All existing features of CastMate
- Streaming companion functionality
- Integration capabilities
- User-facing features and workflows

Assess the documentation and provide:

1. **Completion Score** (0-100): How well does the documentation fulfill the user's specific request: "${originalDescription}"?
   - Does it provide the comprehensive product overview requested?
   - Are all existing CastMate features covered?
   - Is the documentation scope appropriate for the user's needs?

2. **Missing Areas**: What important aspects from the user's request are not covered?
   - Specific CastMate features not documented
   - Product overview elements missing
   - User workflows not explained

3. **Quality Issues**: Any problems with the existing documentation relative to the user's request?
   - Misalignment with user's stated goals
   - Insufficient detail for requested scope
   - Generic content not specific to CastMate

4. **Additional Agents Needed**: If the user's request is not fully satisfied, what specialized agents should be created?

For any recommended additional agents, provide:
- Unique ID (kebab-case)
- Descriptive name
- Specific description of what they should document
- Specialization area
- Capabilities list

Return a JSON object with this structure:
{
  "isComplete": boolean,
  "completionScore": number,
  "missingAreas": ["area1", "area2"],
  "qualityIssues": ["issue1", "issue2"],
  "recommendedAdditionalAgents": [
    {
      "id": "agent-id",
      "name": "Agent Name",
      "description": "What this agent should document",
      "specialization": "Area of expertise",
      "capabilities": ["capability1", "capability2"]
    }
  ],
  "overallFeedback": "Summary of how well the documentation fulfills the user's specific request for '${originalDescription}'"
}

CRITICAL: Your assessment must be centered on the user's specific request: "${originalDescription}"
- Focus on whether the documentation provides what the user actually asked for
- Evaluate completeness relative to the user's stated goals
- Only recommend additional agents if they would help fulfill the user's specific request
- Be thorough but practical in your evaluation
`;

    try {
      const result = await processWithGoogleAI({
        prompt,
        model: 'gemini-2.5-pro'
      });

      // Handle potential fallback response
      if (result.startsWith('FALLBACK_REQUIRED:')) {
        console.warn('Google AI fallback triggered, using Claude for assessment');
        const claudeResult = await processWithAnthropic({
          prompt,
          model: "claude-sonnet-4-20250514",
          modelOptions: {
            temperature: 0.3,
            maxTokens: 2000
          }
        });

        try {
          const rawAssessment = JSON.parse(claudeResult.trim());
          return DocumentationAssessmentSchema.parse(rawAssessment);
        } catch (validationError) {
          console.error('Invalid assessment from Claude fallback:', validationError);
          throw validationError;
        }
      }

      try {
        // Clean the result by removing markdown code blocks if present
        let cleanedResult = result.trim();
        if (cleanedResult.startsWith('```json')) {
          cleanedResult = cleanedResult.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        } else if (cleanedResult.startsWith('```')) {
          cleanedResult = cleanedResult.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }

        const rawAssessment = JSON.parse(cleanedResult);
        return DocumentationAssessmentSchema.parse(rawAssessment);
      } catch (validationError) {
        console.error('Invalid assessment from Google AI:', validationError);
        console.error('Raw result:', result);
        throw validationError;
      }
    } catch (error: any) {
      console.error('Failed to assess documentation completeness:', error);
      // Return a default "complete" assessment to avoid infinite loops
      return {
        isComplete: true,
        completionScore: 75,
        missingAreas: [],
        qualityIssues: [],
        recommendedAdditionalAgents: [],
        overallFeedback: 'Assessment failed, assuming documentation is adequate.'
      };
    }
  }

  /**
   * Execute all sub-agent assignments
   */
  private async _executeSubAgentAssignments(
    assignments: SubAgentAssignment[],
    originalRequest?: string
  ): Promise<Map<string, SubAgentResult>> {
    const results = new Map<string, SubAgentResult>();

    for (let i = 0; i < assignments.length; i++) {
      const assignment = assignments[i];

      this._updateStream('processing-assignments', {
        subAgentProgress: {
          completed: i,
          total: assignments.length,
          currentAgent: assignment.agentName
        }
      }, `Processing ${assignment.agentName}...`);

      try {
        const result = await this._executeSubAgentAssignment(assignment, originalRequest);
        results.set(assignment.agentId, result);
      } catch (error: any) {
        console.error(`Failed to execute ${assignment.agentName}:`, error);
        results.set(assignment.agentId, {
          agentId: assignment.agentId,
          agentName: assignment.agentName,
          assignment: assignment.assignment,
          output: '',
          success: false,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Index the codebase into the RAG system for semantic search
   */
  private async _indexCodebaseForRAG(selectedPaths: string[], description: string): Promise<void> {
    try {
      // Determine the root path (assume first path or current working directory)
      const rootPath = selectedPaths.length > 0 ?
        path.dirname(selectedPaths[0]) :
        process.cwd();

      const projectName = path.basename(rootPath) || 'codebase';

      const result = await codebaseIndexingTool.indexCodebase({
        rootPath,
        userId: this.options.userId,
        projectName: `${projectName}_documentation`,
        excludePatterns: [
          'node_modules', '.git', '.next', 'dist', 'build',
          '.vscode', 'coverage', '.nuxt', '.output', '__pycache__'
        ],
        includeExtensions: ['.ts', '.tsx', '.js', '.jsx', '.py', '.md', '.json', '.yaml', '.yml']
      });

      if (result.success) {
        this.codebaseDocumentId = result.documentId;
        console.log(`Successfully indexed ${result.totalFiles} files into ${result.totalChunks} chunks`);
      } else {
        console.warn('Failed to index codebase:', result.error);
        // Continue without RAG - sub-agents will work with basic prompts
      }
    } catch (error) {
      console.error('Error indexing codebase for RAG:', error);
      // Continue without RAG - sub-agents will work with basic prompts
    }
  }

  /**
   * Execute a single sub-agent assignment with RAG-enhanced context
   */
  private async _executeSubAgentAssignment(assignment: SubAgentAssignment, originalRequest?: string): Promise<SubAgentResult> {
    // Get relevant code context using semantic search
    const relevantContext = await this._getRelevantCodeContext(assignment);

    const prompt = this._getSubAgentPrompt(assignment, originalRequest, relevantContext);

    try {
      const result = await processWithAnthropic({
        prompt,
        model: "claude-sonnet-4-20250514",
        modelOptions: {
          temperature: 0.4,
          maxTokens: 3500 // Increased for more context
        }
      });

      const subAgentResult = {
        agentId: assignment.agentId,
        agentName: assignment.agentName,
        assignment: assignment.assignment,
        output: result,
        success: true
      };

      // Validate the sub-agent result using Zod schema
      try {
        return SubAgentResultSchema.parse(subAgentResult);
      } catch (validationError) {
        console.warn(`Sub-agent result validation failed for ${assignment.agentName}:`, validationError);
        // Return the result anyway but log the validation issue
        return subAgentResult;
      }
    } catch (error: any) {
      throw new Error(`Sub-agent ${assignment.agentName} failed: ${error.message}`);
    }
  }

  /**
   * Get relevant code context for a sub-agent assignment using semantic search
   */
  private async _getRelevantCodeContext(assignment: SubAgentAssignment): Promise<string> {
    if (!this.codebaseDocumentId) {
      return "No indexed codebase available. Please analyze based on file structure and general patterns.";
    }

    try {
      // Create search queries based on the assignment
      const searchQueries = this._generateSearchQueries(assignment);

      let allRelevantContent = '';

      for (const query of searchQueries) {
        const result = await queryDocumentsTool.process({
          userId: this.options.userId,
          query,
          namespace: 'Codebase Documentation',
          maxResults: 5
        });

        if (result.success && result.sources && result.sources.length > 0) {
          allRelevantContent += `\n\n=== RELEVANT CODE FOR "${query}" ===\n`;
          result.sources.forEach((source, index) => {
            allRelevantContent += `\n--- Document ${index + 1} (${source.title || 'Unknown'}) ---\n`;
            allRelevantContent += result.content.substring(0, 2000); // Limit content size
            allRelevantContent += '\n';
          });
        }
      }

      return allRelevantContent || "No relevant code found in indexed codebase.";
    } catch (error) {
      console.error('Error retrieving relevant code context:', error);
      return "Error retrieving code context. Please analyze based on general patterns.";
    }
  }

  /**
   * Generate search queries based on sub-agent assignment
   */
  private _generateSearchQueries(assignment: SubAgentAssignment): string[] {
    const queries: string[] = [];

    // Base query from assignment
    queries.push(assignment.assignment);

    // Specialization-based queries
    if (assignment.specialization.toLowerCase().includes('architecture')) {
      queries.push('class definition interface component architecture');
      queries.push('import export module dependency');
    } else if (assignment.specialization.toLowerCase().includes('api')) {
      queries.push('api endpoint route handler controller');
      queries.push('request response middleware');
    } else if (assignment.specialization.toLowerCase().includes('data')) {
      queries.push('database model schema entity');
      queries.push('data flow state management');
    } else if (assignment.specialization.toLowerCase().includes('security')) {
      queries.push('authentication authorization security');
      queries.push('validation sanitization encryption');
    } else if (assignment.specialization.toLowerCase().includes('performance')) {
      queries.push('optimization performance caching');
      queries.push('async await promise concurrent');
    }

    // Add path-specific queries
    assignment.requiredPaths.forEach(path => {
      const pathParts = path.split('/').filter(part => part && !part.includes('.'));
      if (pathParts.length > 0) {
        queries.push(pathParts.join(' '));
      }
    });

    return queries.slice(0, 3); // Limit to 3 queries to avoid too much context
  }

  /**
   * Get specialized prompt for dynamic sub-agents with enhanced Claude reasoning and RAG context
   */
  private _getSubAgentPrompt(assignment: SubAgentAssignment, originalRequest?: string, relevantContext?: string): string {
    const contextSection = relevantContext && relevantContext.trim() !== "No indexed codebase available. Please analyze based on file structure and general patterns."
      ? `

📋 RELEVANT CODEBASE CONTEXT:
${relevantContext}

🔍 IMPORTANT: Use the above code context to ground your analysis in the actual codebase implementation. Reference specific code patterns, functions, classes, and structures you see in the context.
`
      : `

⚠️ LIMITED CONTEXT: No specific code content is available. Base your analysis on general software engineering patterns and the file structure provided.
`;

    const basePrompt = `You are a ${assignment.agentName} specialized in ${assignment.specialization} with deep technical expertise in codebase documentation.

🎯 USER'S ORIGINAL REQUEST: ${originalRequest || 'Comprehensive codebase documentation'}

AGENT SPECIALIZATION: ${assignment.specialization}

SPECIFIC ASSIGNMENT: ${assignment.assignment}

REQUIRED PATHS: ${assignment.requiredPaths.join(', ')}
${contextSection}

🔍 CRITICAL: Your documentation must directly support the user's original request: "${originalRequest || 'Comprehensive codebase documentation'}"

Please analyze the codebase systematically and provide comprehensive documentation that fulfills the user's specific needs. Use your reasoning capabilities to:

1. **Align with User Request**: Ensure your analysis directly supports what the user asked for
2. **Identify Relevant Patterns**: Focus on patterns and relationships that matter for the user's request
3. **Provide Contextual Insights**: Highlight architectural decisions and rationale relevant to the user's goals
4. **Address User Needs**: Focus on aspects that directly fulfill the user's documentation requirements
5. **Deliver Actionable Content**: Provide insights that help achieve the user's stated objectives

🎯 **Your Primary Goal**: Create documentation that directly addresses the user's request while leveraging your specialization in ${assignment.specialization}.

Provide detailed, well-structured documentation that:
- Directly supports the user's original request
- Uses your specialized knowledge effectively
- Includes relevant code examples and technical details from the provided context
- Is formatted clearly with markdown
- References specific code elements when available in the context

Your response should be comprehensive yet focused on fulfilling the user's specific documentation needs.
`;

    return basePrompt;
  }

  /**
   * Fix common LLM mistakes in assignment values
   */
  private _fixAssignmentValues(assignment: any): any {
    const fixed = { ...assignment };

    // Fix estimatedComplexity values
    if (fixed.estimatedComplexity === 'high') {
      fixed.estimatedComplexity = 'complex';
    }
    if (fixed.estimatedComplexity === 'low') {
      fixed.estimatedComplexity = 'simple';
    }
    if (fixed.estimatedComplexity === 'medium') {
      fixed.estimatedComplexity = 'moderate';
    }

    // Ensure required fields exist
    if (!fixed.agentId || typeof fixed.agentId !== 'string') {
      fixed.agentId = `agent-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }

    if (!fixed.assignment || fixed.assignment.length < 10) {
      fixed.assignment = 'Analyze and document the specified codebase components';
    }

    if (!fixed.priority || !['high', 'medium', 'low'].includes(fixed.priority)) {
      fixed.priority = 'medium';
    }

    if (!fixed.estimatedComplexity || !['simple', 'moderate', 'complex'].includes(fixed.estimatedComplexity)) {
      fixed.estimatedComplexity = 'moderate';
    }

    if (!Array.isArray(fixed.requiredPaths)) {
      fixed.requiredPaths = ['src'];
    }

    if (!fixed.specialization || typeof fixed.specialization !== 'string') {
      fixed.specialization = 'General Documentation';
    }

    return fixed;
  }

  /**
   * Create a fallback assignment from a failed assignment
   */
  private _createFallbackAssignment(failedAssignment: any, selectedPaths: string[]): SubAgentAssignment | null {
    try {
      const fallback: SubAgentAssignment = {
        agentId: failedAssignment.agentId || `fallback-agent-${Date.now()}`,
        agentName: failedAssignment.agentName || 'Documentation Agent',
        assignment: failedAssignment.assignment || 'Analyze and document codebase components',
        priority: 'medium',
        estimatedComplexity: 'moderate',
        requiredPaths: selectedPaths.slice(0, 3),
        specialization: failedAssignment.specialization || 'General Documentation'
      };

      return fallback;
    } catch (error) {
      console.warn('Failed to create fallback assignment:', error);
      return null;
    }
  }

  /**
   * Create fallback assignments if LLM generation fails
   */
  private _createFallbackAssignments(selectedPaths: string[]): SubAgentAssignment[] {
    return [
      {
        agentId: 'project-overview-analyst',
        agentName: 'Project Overview Analyst',
        assignment: 'Analyze project structure and provide high-level overview',
        priority: 'high',
        estimatedComplexity: 'simple',
        requiredPaths: selectedPaths.slice(0, 3),
        specialization: 'Project Analysis'
      },
      {
        agentId: 'architecture-analyst',
        agentName: 'Architecture Analyst',
        assignment: 'Document system architecture and design patterns',
        priority: 'high',
        estimatedComplexity: 'complex',
        requiredPaths: selectedPaths,
        specialization: 'System Architecture'
      }
      // Add more fallback assignments as needed
    ];
  }

  /**
   * Consolidate all sub-agent results into final documentation
   */
  private async _consolidateDocumentation(
    subAgentResults: Map<string, SubAgentResult>,
    codebaseAnalysis: CodebaseAnalysisResult,
    originalDescription: string
  ): Promise<any> {
    // Consolidate results into structured documentation artifacts
    const artifacts: any = {};

    subAgentResults.forEach((result, agentId) => {
      if (result.success) {
        // Use agent specialization to categorize the output
        const specialization = result.agentName.toLowerCase();

        if (specialization.includes('overview') || specialization.includes('project')) {
          artifacts.projectOverview = result.output;
        } else if (specialization.includes('architecture')) {
          artifacts.architectureAnalysis = result.output;
        } else if (specialization.includes('component') || specialization.includes('mapping')) {
          artifacts.componentMapping = result.output;
        } else if (specialization.includes('business') || specialization.includes('logic')) {
          artifacts.businessLogicAnalysis = result.output;
        } else if (specialization.includes('data') || specialization.includes('flow')) {
          artifacts.dataFlowDiagram = result.output;
        } else if (specialization.includes('environment') || specialization.includes('technical')) {
          artifacts.technicalEnvironment = result.output;
        } else if (specialization.includes('dependencies') || specialization.includes('dependency')) {
          artifacts.dependenciesAnalysis = result.output;
        } else if (specialization.includes('api') || specialization.includes('interface')) {
          artifacts.apiDocumentation = result.output;
        } else if (specialization.includes('configuration') || specialization.includes('config')) {
          artifacts.configurationAnalysis = result.output;
        } else {
          // For dynamic agents, create a section based on their name
          const sectionKey = agentId.replace(/-/g, '');
          artifacts[sectionKey] = result.output;
        }
      }
    });

    return artifacts;
  }

  /**
   * Generate supporting charts dynamically based on documentation content
   */
  private async _generateDynamicDocumentationCharts(
    codebaseAnalysis: CodebaseAnalysisResult,
    artifacts: any,
    subAgentResults: Map<string, SubAgentResult>
  ): Promise<ChartGenerationResult[]> {
    const charts: ChartGenerationResult[] = [];

    try {
      // Use Claude to determine what charts would be most valuable
      const chartRecommendationPrompt = `
You are a Data Visualization Expert. Based on the codebase analysis and documentation content,
determine what charts and visualizations would be most valuable for this documentation.

CODEBASE ANALYSIS:
- Total Files: ${codebaseAnalysis.totalFiles}
- Languages: ${codebaseAnalysis.languages.join(', ')}
- Complexity: ${codebaseAnalysis.complexity}
- Frameworks: ${codebaseAnalysis.frameworks.join(', ')}

DOCUMENTATION SECTIONS AVAILABLE:
${Object.keys(artifacts).map(key => `- ${key}`).join('\n')}

AGENT SPECIALIZATIONS:
${Array.from(subAgentResults.values()).map(result => `- ${result.agentName}: ${result.assignment.substring(0, 100)}...`).join('\n')}

Recommend 2-4 specific charts that would enhance this documentation. For each chart, provide:
1. A natural language description of what to visualize
2. The type of chart (pie, bar, line, scatter, etc.)
3. Why this chart would be valuable

Return a JSON array with this structure:
[
  {
    "description": "Natural language description for chart generation",
    "type": "chart_type",
    "rationale": "Why this chart is valuable"
  }
]
`;

      const chartRecommendations = await processWithAnthropic({
        prompt: chartRecommendationPrompt,
        model: "claude-sonnet-4-20250514",
        modelOptions: {
          temperature: 0.4,
          maxTokens: 1500
        }
      });

      let recommendations: ChartRecommendationType[] = [];
      try {
        const rawRecommendations = JSON.parse(chartRecommendations.trim());

        // Validate each recommendation using Zod
        for (const rec of rawRecommendations) {
          try {
            const validated = ChartRecommendationSchema.parse(rec);
            recommendations.push(validated);
          } catch (validationError) {
            console.warn('Invalid chart recommendation from LLM, skipping:', rec, validationError);
          }
        }

        if (recommendations.length === 0) {
          throw new Error('No valid recommendations');
        }
      } catch (error) {
        console.warn('Failed to parse chart recommendations, using defaults');
        recommendations = [
          {
            description: `Create a pie chart showing the language distribution for a codebase with the following languages: ${codebaseAnalysis.languages.join(', ')}. Use appropriate colors and show percentages.`,
            type: 'pie',
            rationale: 'Shows technology stack composition'
          },
          {
            description: `Create a bar chart showing codebase metrics with the following data: Total Files: ${codebaseAnalysis.totalFiles}, Total Lines: ${codebaseAnalysis.totalLines.toLocaleString()}. Make it visually appealing with appropriate colors.`,
            type: 'bar',
            rationale: 'Provides scale overview'
          }
        ];
      }

      // Generate each recommended chart
      for (const recommendation of recommendations.slice(0, 4)) { // Limit to 4 charts
        try {
          const chart = await this.chartTool.generateChart({
            prompt: recommendation.description,
            chartType: recommendation.type
          });

          if (chart.success) {
            charts.push(chart);
          }
        } catch (error) {
          console.warn(`Failed to generate chart: ${recommendation.description}`, error);
        }
      }

    } catch (error) {
      console.warn('Failed to generate dynamic documentation charts:', error);
    }

    return charts;
  }

  /**
   * Format final output for display
   */
  private _formatFinalOutput(artifacts: any, codebaseAnalysis: CodebaseAnalysisResult): string {
    let output = `# Codebase Documentation\n\n`;
    
    output += `## Overview\n`;
    output += `- **Total Files**: ${codebaseAnalysis.totalFiles}\n`;
    output += `- **Total Lines**: ${codebaseAnalysis.totalLines.toLocaleString()}\n`;
    output += `- **Languages**: ${codebaseAnalysis.languages.join(', ')}\n`;
    output += `- **Complexity**: ${codebaseAnalysis.complexity}\n`;
    output += `- **Frameworks**: ${codebaseAnalysis.frameworks.join(', ')}\n\n`;

    if (artifacts.projectOverview) {
      output += `## Project Overview\n${artifacts.projectOverview}\n\n`;
    }

    if (artifacts.architectureAnalysis) {
      output += `## Architecture Analysis\n${artifacts.architectureAnalysis}\n\n`;
    }

    if (artifacts.componentMapping) {
      output += `## Component Mapping\n${artifacts.componentMapping}\n\n`;
    }

    if (artifacts.businessLogicAnalysis) {
      output += `## Business Logic Analysis\n${artifacts.businessLogicAnalysis}\n\n`;
    }

    if (artifacts.dataFlowDiagram) {
      output += `## Data Flow Analysis\n${artifacts.dataFlowDiagram}\n\n`;
    }

    if (artifacts.technicalEnvironment) {
      output += `## Technical Environment\n${artifacts.technicalEnvironment}\n\n`;
    }

    if (artifacts.dependenciesAnalysis) {
      output += `## Dependencies Analysis\n${artifacts.dependenciesAnalysis}\n\n`;
    }

    if (artifacts.apiDocumentation) {
      output += `## API Documentation\n${artifacts.apiDocumentation}\n\n`;
    }

    if (artifacts.configurationAnalysis) {
      output += `## Configuration Analysis\n${artifacts.configurationAnalysis}\n\n`;
    }

    return output;
  }

  /**
   * Update stream if streaming is enabled
   */
  private _updateStream(
    stage: CodebaseDocumentationStreamUpdate['stage'],
    data?: any,
    message?: string
  ): void {
    if (this.options.streamResponse && this.options.onStreamUpdate) {
      this.options.onStreamUpdate({
        stage,
        data,
        message
      });
    }
  }
}
