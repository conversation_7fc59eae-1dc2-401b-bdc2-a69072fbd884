import { StorageTool } from './storage-tool';
import { promises as fs } from 'fs';
import path from 'path';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';

export interface CodebaseIndexingOptions {
  rootPath: string;
  userId: string;
  projectName: string;
  excludePatterns?: string[];
  includeExtensions?: string[];
  chunkSize?: number;
  chunkOverlap?: number;
}

export interface CodebaseIndexingResult {
  success: boolean;
  totalFiles: number;
  totalChunks: number;
  documentId: string;
  error?: string;
}

export class CodebaseIndexingTool {
  private storageTool: StorageTool;
  private defaultExcludePatterns = [
    'node_modules',
    '.git',
    '.next',
    'dist',
    'build',
    '.vscode',
    'coverage',
    '.nuxt',
    '.output',
    '__pycache__',
    '.env',
    '.env.local',
    '.env.production',
    'package-lock.json',
    'yarn.lock',
    'pnpm-lock.yaml'
  ];

  private defaultIncludeExtensions = [
    '.ts', '.tsx', '.js', '.jsx',
    '.py', '.java', '.cpp', '.c',
    '.cs', '.go', '.rs', '.php',
    '.rb', '.swift', '.kt', '.scala',
    '.md', '.txt', '.json', '.yaml', '.yml'
  ];

  constructor() {
    this.storageTool = new StorageTool();
  }

  /**
   * Index an entire codebase into the RAG system
   */
  async indexCodebase(options: CodebaseIndexingOptions): Promise<CodebaseIndexingResult> {
    try {
      console.log(`Starting codebase indexing for ${options.projectName}`);
      
      const excludePatterns = options.excludePatterns || this.defaultExcludePatterns;
      const includeExtensions = options.includeExtensions || this.defaultIncludeExtensions;
      
      // Recursively find all relevant files
      const files = await this.findCodeFiles(
        options.rootPath,
        excludePatterns,
        includeExtensions
      );
      
      console.log(`Found ${files.length} code files to index`);

      // Limit files for large codebases to prevent hanging
      const maxFiles = 200; // Reasonable limit for initial indexing
      const filesToProcess = files.length > maxFiles ? files.slice(0, maxFiles) : files;

      if (files.length > maxFiles) {
        console.log(`⚠️ Large codebase detected. Processing first ${maxFiles} files to prevent timeout.`);
        console.log(`💡 Consider running multiple smaller indexing operations for complete coverage.`);
      }

      // Process files in batches to avoid memory issues
      const batchSize = 10;
      let totalChunks = 0;
      const allContent: string[] = [];
      
      for (let i = 0; i < filesToProcess.length; i += batchSize) {
        const batch = filesToProcess.slice(i, i + batchSize);
        const batchContent = await this.processBatch(batch, options.rootPath);
        allContent.push(...batchContent);
        totalChunks += batchContent.length;

        console.log(`Processed batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(filesToProcess.length/batchSize)}`);
      }
      
      // Combine all content with file separators
      console.log('Combining content from all processed files...');
      const combinedContent = allContent.join('\n\n---FILE_SEPARATOR---\n\n');
      console.log(`Combined content size: ${combinedContent.length} characters`);

      // Save to RAG system using existing infrastructure with timeout
      console.log('Saving to RAG system (this may take a while for large codebases)...');

      const savePromise = this.storageTool.savePdfToByteStore(
        Buffer.from(combinedContent, 'utf-8'),
        `${options.projectName}_codebase`,
        combinedContent,
        'Codebase Documentation',
        {
          projectName: options.projectName,
          indexedAt: new Date().toISOString(),
          totalFiles: files.length,
          rootPath: options.rootPath,
          type: 'codebase_index'
        }
      );

      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Indexing timeout after 10 minutes')), 10 * 60 * 1000);
      });

      const result = await Promise.race([savePromise, timeoutPromise]) as any;

      console.log(`✅ Successfully indexed codebase!`);
      console.log(`📊 Total files: ${files.length}`);
      console.log(`📄 Total chunks: ${result.totalChunks}`);
      console.log(`🆔 Document ID: ${result.documentId}`);

      return {
        success: true,
        totalFiles: files.length,
        totalChunks: result.totalChunks,
        documentId: result.documentId
      };
      
    } catch (error) {
      console.error('Error indexing codebase:', error);
      return {
        success: false,
        totalFiles: 0,
        totalChunks: 0,
        documentId: '',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Find all code files in a directory tree
   */
  private async findCodeFiles(
    rootPath: string,
    excludePatterns: string[],
    includeExtensions: string[]
  ): Promise<string[]> {
    const files: string[] = [];
    
    const traverse = async (currentPath: string): Promise<void> => {
      try {
        const items = await fs.readdir(currentPath, { withFileTypes: true });
        
        for (const item of items) {
          const fullPath = path.join(currentPath, item.name);
          
          if (item.isDirectory()) {
            // Skip excluded directories
            if (excludePatterns.some(pattern => item.name.includes(pattern))) {
              continue;
            }
            await traverse(fullPath);
          } else if (item.isFile()) {
            // Include files with matching extensions
            const ext = path.extname(item.name);
            if (includeExtensions.includes(ext)) {
              files.push(fullPath);
            }
          }
        }
      } catch (error) {
        console.warn(`Could not read directory: ${currentPath}`);
      }
    };
    
    await traverse(rootPath);
    return files;
  }

  /**
   * Process a batch of files and extract their content
   */
  private async processBatch(filePaths: string[], rootPath: string): Promise<string[]> {
    const contents: string[] = [];
    
    for (const filePath of filePaths) {
      try {
        const content = await fs.readFile(filePath, 'utf-8');
        const relativePath = path.relative(rootPath, filePath);
        
        // Create a structured representation of the file
        const fileContent = `
=== FILE: ${relativePath} ===
Language: ${this.getLanguageFromExtension(path.extname(filePath))}
Path: ${relativePath}
Size: ${content.length} characters

${content}

=== END FILE: ${relativePath} ===
`;
        
        contents.push(fileContent);
      } catch (error) {
        console.warn(`Could not read file: ${filePath}`);
      }
    }
    
    return contents;
  }

  /**
   * Get programming language from file extension
   */
  private getLanguageFromExtension(ext: string): string {
    const languageMap: Record<string, string> = {
      '.ts': 'TypeScript',
      '.tsx': 'TypeScript React',
      '.js': 'JavaScript',
      '.jsx': 'JavaScript React',
      '.py': 'Python',
      '.java': 'Java',
      '.cpp': 'C++',
      '.c': 'C',
      '.cs': 'C#',
      '.go': 'Go',
      '.rs': 'Rust',
      '.php': 'PHP',
      '.rb': 'Ruby',
      '.swift': 'Swift',
      '.kt': 'Kotlin',
      '.scala': 'Scala',
      '.md': 'Markdown',
      '.txt': 'Text',
      '.json': 'JSON',
      '.yaml': 'YAML',
      '.yml': 'YAML'
    };
    
    return languageMap[ext] || 'Unknown';
  }
}

export const codebaseIndexingTool = new CodebaseIndexingTool();
